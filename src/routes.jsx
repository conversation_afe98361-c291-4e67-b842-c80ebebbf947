import { createBrowserRouter, Navigate } from 'react-router-dom';
import { lazy, Suspense } from 'react';
import ProtectedRoute from './components/ProtectedRoute';
import ErrorBoundary from './components/ErrorBoundary';
import App from './App';

// Componente de carga para usar con Suspense
import LoadingSpinner from './components/LoadingSpinner';
import logger from './services/logService';

// Enhanced lazy loading with retry mechanism for MIME type issues
const lazyWithRetry = (componentImport) => {
  return lazy(async () => {
    const pageHasAlreadyBeenForceRefreshed = JSON.parse(
      window.sessionStorage.getItem('page-has-been-force-refreshed') || 'false'
    );

    try {
      const component = await componentImport();
      window.sessionStorage.setItem('page-has-been-force-refreshed', 'false');
      return component;
    } catch (error) {
      if (!pageHasAlreadyBeenForceRefreshed) {
        // If this is the first time the import failed, refresh the page
        window.sessionStorage.setItem('page-has-been-force-refreshed', 'true');
        logger.error('Failed to load component, refreshing page:', error);
        window.location.reload();
        return { default: () => null };
      } else {
        // If the page has already been refreshed and it still fails, throw the error
        logger.error('Failed to load component after refresh:', error);
        throw error;
      }
    }
  });
};

// Importación de páginas con lazy loading y retry mechanism
const HomePage = lazyWithRetry(() => import('./pages/HomePage'));
const LoginPage = lazyWithRetry(() => import('./pages/LoginPage'));
const AdminPage = lazyWithRetry(() => import('./pages/AdminPage'));
const CreateModeloPage = lazyWithRetry(() => import('./pages/CreateModeloPage'));
const EditModeloPage = lazyWithRetry(() => import('./pages/EditModeloPage'));
const CreateBannerPage = lazyWithRetry(() => import('./pages/CreateBannerPage'));
const EditBannerPage = lazyWithRetry(() => import('./pages/EditBannerPage'));
const ModeloDetailPage = lazyWithRetry(() => import('./pages/ModeloDetailPage'));
const BrujitaDetailPage = lazyWithRetry(() => import('./pages/BrujitaDetailPage'));
const WelcomePage = lazyWithRetry(() => import('./pages/WelcomePage'));
// Import Independiente Pages
const CreateIndependientePage = lazyWithRetry(() => import('./pages/CreateIndependientePage'));
const EditIndependientePage = lazyWithRetry(() => import('./pages/EditIndependientePage'));
const IndependienteDetailPage = lazyWithRetry(() => import('./pages/IndependienteDetailPage'));
const IndependientesPage = lazyWithRetry(() => import('./pages/IndependientesPage'));
const ClientesVipPage = lazyWithRetry(() => import('./pages/ClientesVipPage'));
// Import Ofertas Pages
const OfertasPage = lazyWithRetry(() => import('./pages/OfertasPage'));
const CreateOfertaPage = lazyWithRetry(() => import('./pages/CreateOfertaPage'));
const EditOfertaPage = lazyWithRetry(() => import('./pages/EditOfertaPage'));

/**
 * Función para envolver componentes con ErrorBoundary y Suspense
 * Suspense muestra el LoadingSpinner mientras se carga el componente lazy
 */
const withErrorBoundary = (component) => (
  <ErrorBoundary>
    <Suspense fallback={<LoadingSpinner />}>
      {component}
    </Suspense>
  </ErrorBoundary>
);

/**
 * Configuración de rutas de la aplicación
 * Las rutas administrativas están protegidas y requieren autenticación
 * Todas las rutas están envueltas en un ErrorBoundary para capturar errores
 */
const router = createBrowserRouter([
  {
    path: '/',
    element: <App />,
    children: [
      {
        path: '',
        element: withErrorBoundary(<HomePage />),
      },
      {
        path: 'bienvenida',
        element: withErrorBoundary(<WelcomePage />),
      },
      {
        path: 'welcome',
        element: withErrorBoundary(
          <Navigate to="/bienvenida" replace />
        ),
      },
      {
        path: 'login',
        element: withErrorBoundary(<LoginPage />),
      },
      {
        path: 'modelo/:id',
        element: withErrorBoundary(<ModeloDetailPage />),
      },
      {
        path: 'brujita/:id',
        element: withErrorBoundary(<BrujitaDetailPage />),
      },
      {
        path: 'independientes',
        element: withErrorBoundary(<IndependientesPage />),
      },
      {
        path: 'independiente/:id', // New route for Independiente detail
        element: withErrorBoundary(<IndependienteDetailPage />),
      },
      {
        path: 'clientesvip',
        element: withErrorBoundary(<ClientesVipPage />),
      },
      {
        path: 'ofertas',
        element: withErrorBoundary(<OfertasPage />),
      },
      // Rutas protegidas que requieren autenticación
      {
        path: 'admin',
        element: withErrorBoundary(
          <ProtectedRoute>
            <AdminPage />
          </ProtectedRoute>
        ),
      },
      {
        path: 'admin/nueva-brujita',
        element: withErrorBoundary(
          <ProtectedRoute>
            <CreateModeloPage />
          </ProtectedRoute>
        ),
      },
      {
        path: 'admin/editar-brujita/:id',
        element: withErrorBoundary(
          <ProtectedRoute>
            <EditModeloPage />
          </ProtectedRoute>
        ),
      },
      {
        path: 'admin/nueva-independiente', // New route for creating Independiente
        element: withErrorBoundary(
          <ProtectedRoute>
            <CreateIndependientePage />
          </ProtectedRoute>
        ),
      },
      {
        path: 'admin/editar-independiente/:id', // New route for editing Independiente
        element: withErrorBoundary(
          <ProtectedRoute>
            <EditIndependientePage />
          </ProtectedRoute>
        ),
      },
      {
        path: 'admin/nuevo-banner',
        element: withErrorBoundary(
          <ProtectedRoute>
            <CreateBannerPage />
          </ProtectedRoute>
        ),
      },
      {
        path: 'admin/editar-banner/:id',
        element: withErrorBoundary(
          <ProtectedRoute>
            <EditBannerPage />
          </ProtectedRoute>
        ),
      },
      {
        path: 'admin/nueva-oferta',
        element: withErrorBoundary(
          <ProtectedRoute>
            <CreateOfertaPage />
          </ProtectedRoute>
        ),
      },
      {
        path: 'admin/editar-oferta/:id',
        element: withErrorBoundary(
          <ProtectedRoute>
            <EditOfertaPage />
          </ProtectedRoute>
        ),
      },
      // Ruta para manejar páginas no encontradas
      {
        path: '*',
        element: withErrorBoundary(
          <div className="min-h-screen bg-[#121220] text-white flex items-center justify-center p-4">
            <div className="max-w-md w-full bg-[#181828] p-8 rounded-lg shadow-lg border border-[#29293d] text-center">
              <h1 className="text-3xl font-bold text-[#e0b3ff] mb-4">404</h1>
              <p className="text-xl text-gray-300 mb-6">Página no encontrada</p>
              <a
                href="/"
                className="px-6 py-3 bg-gradient-to-r from-[#9c27b0] to-[#e91e63] text-white rounded-lg inline-block hover:opacity-90 transition-all"
              >
                Volver al inicio
              </a>
            </div>
          </div>
        ),
      },
    ]
  },
]);

export default router;
