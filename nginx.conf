# Nginx configuration for React SPA with proper MIME types
server {
    listen 80;
    listen [::]:80;
    server_name brujitassexysmx.com www.brujitassexysmx.com;
    
    # Document root
    root /var/www/html;
    index index.html;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # Set proper MIME types
    location ~* \.(js|mjs)$ {
        add_header Content-Type "application/javascript; charset=utf-8";
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location ~* \.css$ {
        add_header Content-Type "text/css; charset=utf-8";
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location ~* \.json$ {
        add_header Content-Type "application/json; charset=utf-8";
        expires 1d;
    }

    location ~* \.svg$ {
        add_header Content-Type "image/svg+xml; charset=utf-8";
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Cache static assets
    location ~* \.(png|jpg|jpeg|gif|ico|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()";

    # Handle React Router (SPA routing)
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API proxy (if needed)
    location /api/ {
        proxy_pass https://db.brujitassexysmx.com/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Prevent access to sensitive files
    location ~ /\. {
        deny all;
    }

    location ~ \.(bak|config|dist|fla|inc|ini|log|psd|sh|sql|sw[op])$ {
        deny all;
    }
}
