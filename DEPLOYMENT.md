# Deployment Guide - Brujitas Application

This guide explains how to deploy the Brujitas React application and fix MIME type issues that cause JavaScript module loading errors.

## The Problem

The error you're seeing:
```
Failed to load module script: Expected a JavaScript module script but the server responded with a MIME type of "text/html"
```

This happens when the web server serves JavaScript files with the wrong MIME type, causing the browser to reject dynamically imported modules.

## Solutions Implemented

### 1. Enhanced Lazy Loading with Retry Mechanism

The application now includes a retry mechanism that automatically refreshes the page if a module fails to load, which often resolves temporary MIME type issues.

### 2. Server Configuration Files

Several server configuration files have been created to ensure proper MIME types:

#### Apache (.htaccess)
- Sets `application/javascript` MIME type for `.js` and `.mjs` files
- Handles SPA routing
- Includes security headers and caching

#### Nginx (nginx.conf)
- Proper MIME type configuration
- Gzip compression
- SPA routing support

#### Netlify (_headers and _redirects)
- MIME type headers for static files
- SPA routing redirects
- API proxy configuration

#### IIS (web.config)
- MIME type mappings
- URL rewriting for SPA

### 3. Build Configuration Updates

Updated `vite.config.js` to:
- Ensure proper file extensions for chunks
- Configure development and preview servers
- Optimize build output

## Deployment Instructions

### Option 1: Using the Deployment Script

1. Run the deployment script:
```bash
./deploy.sh
```

2. Upload the contents of the `dist` folder to your web server

### Option 2: Manual Deployment

1. Build the application:
```bash
npm run build
```

2. Copy the appropriate server configuration file to your `dist` folder:
   - For Apache: Copy `.htaccess`
   - For Netlify: Files are already in `public/` and will be included
   - For Nginx: Use `nginx.conf` as reference for your server config
   - For IIS: Copy `web.config`

3. Upload the `dist` folder contents to your web server

## Server-Specific Instructions

### Apache
- Ensure `mod_rewrite` and `mod_headers` are enabled
- The `.htaccess` file will be automatically used

### Nginx
- Update your server configuration with the settings from `nginx.conf`
- Restart Nginx after configuration changes

### Netlify
- Simply deploy the `dist` folder
- `_headers` and `_redirects` files will be automatically processed

### IIS
- Ensure URL Rewrite module is installed
- The `web.config` file will be automatically used

### Other Hosting Providers

If using other hosting providers, ensure they:
1. Serve `.js` files with `application/javascript` MIME type
2. Support SPA routing (redirect all non-file requests to `index.html`)
3. Don't interfere with dynamic imports

## Troubleshooting

### If the error persists:

1. **Check server logs** for any errors
2. **Verify MIME types** using browser developer tools
3. **Clear browser cache** and try again
4. **Check network tab** to see what MIME type is being returned
5. **Try a hard refresh** (Ctrl+F5 or Cmd+Shift+R)

### Common Issues:

1. **CDN/Proxy interference**: Some CDNs or proxies might override MIME types
2. **Server configuration not applied**: Ensure your server configuration is active
3. **File permissions**: Ensure the server can read configuration files
4. **Module not found**: Check that all files were uploaded correctly

## Testing

After deployment, test the application by:
1. Visiting the main page
2. Navigating to different routes (especially `/brujita/:id`)
3. Checking browser console for any errors
4. Verifying that lazy-loaded components work correctly

## Support

If you continue experiencing issues:
1. Check the browser's Network tab to see the actual MIME type being served
2. Verify that your hosting provider supports the required server configuration
3. Consider contacting your hosting provider for MIME type configuration assistance

## Files Modified/Created

- `vite.config.js` - Enhanced build configuration
- `src/routes.jsx` - Added retry mechanism for lazy loading
- `.htaccess` - Apache server configuration
- `nginx.conf` - Nginx server configuration
- `public/_headers` - Netlify headers configuration
- `public/_redirects` - Netlify redirects configuration
- `deploy.sh` - Automated deployment script
- `web.config` - IIS server configuration (created during deployment)
