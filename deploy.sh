#!/bin/bash

# Deployment script for Brujitas React application
# This script builds the application and ensures proper MIME types

echo "🚀 Starting deployment process..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf dist/

# Install dependencies
echo "📦 Installing dependencies..."
npm ci

# Build the application
echo "🔨 Building application..."
npm run build

# Check if build was successful
if [ ! -d "dist" ]; then
    echo "❌ Build failed - dist directory not found"
    exit 1
fi

echo "✅ Build completed successfully"

# Copy server configuration files to dist
echo "📋 Copying server configuration files..."

# Copy .htaccess for Apache servers
if [ -f ".htaccess" ]; then
    cp .htaccess dist/
    echo "✅ Copied .htaccess"
fi

# Copy _headers and _redirects for Netlify
if [ -f "public/_headers" ]; then
    cp public/_headers dist/
    echo "✅ Copied _headers for Netlify"
fi

if [ -f "public/_redirects" ]; then
    cp public/_redirects dist/
    echo "✅ Copied _redirects for Netlify"
fi

# Verify JavaScript files have correct extensions
echo "🔍 Verifying JavaScript files..."
js_files=$(find dist -name "*.js" | wc -l)
echo "Found $js_files JavaScript files"

if [ $js_files -eq 0 ]; then
    echo "⚠️  Warning: No JavaScript files found in build"
fi

# Create a simple web.config for IIS servers
cat > dist/web.config << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <staticContent>
            <mimeMap fileExtension=".js" mimeType="application/javascript" />
            <mimeMap fileExtension=".mjs" mimeType="application/javascript" />
            <mimeMap fileExtension=".json" mimeType="application/json" />
        </staticContent>
        <rewrite>
            <rules>
                <rule name="React Routes" stopProcessing="true">
                    <match url=".*" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                        <add input="{REQUEST_URI}" pattern="^/(api)" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="/" />
                </rule>
            </rules>
        </rewrite>
    </system.webServer>
</configuration>
EOF

echo "✅ Created web.config for IIS"

echo "🎉 Deployment preparation completed!"
echo ""
echo "📁 Files ready in 'dist' directory"
echo "🌐 Upload the contents of 'dist' to your web server"
echo ""
echo "📋 Server configuration files included:"
echo "   - .htaccess (Apache)"
echo "   - _headers & _redirects (Netlify)"
echo "   - web.config (IIS)"
echo ""
echo "⚠️  Make sure your server is configured to serve .js files with 'application/javascript' MIME type"
